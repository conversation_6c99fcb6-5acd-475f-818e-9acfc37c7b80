"""
Firebase authentication and API key validation for the prompt construction API.
"""

import logging
from typing import Dict, Optional
from fastapi import HTT<PERSON><PERSON>x<PERSON>
from firebase.config import get_db

logger = logging.getLogger(__name__)

class PromptAPIAuth:
    """
    Authentication handler for the prompt construction API.
    """
    
    def __init__(self):
        """Initialize the authentication handler."""
        self.db = get_db()
        logger.info("✅ Prompt API Auth initialized successfully")
    
    def validate_api_key(self, api_key: str) -> Dict[str, any]:
        """
        Validate an AdMesh API key and return project information.
        
        Args:
            api_key: The API key to validate (format: admesh_[env]_[random])
            
        Returns:
            Dict: Dictionary containing project info and usage data
            
        Raises:
            HTTPException: If the API key is invalid or quota exceeded
        """
        if not api_key or not api_key.startswith('admesh_'):
            raise HTTPException(
                status_code=401, 
                detail="Invalid API key format. Expected format: admesh_[env]_[random]"
            )
        
        try:
            # Query the api_keys collection
            api_keys_ref = self.db.collection('api_keys')
            query = api_keys_ref.where('api_key', '==', api_key).limit(1)
            docs = list(query.stream())
            
            if not docs:
                raise HTTPException(status_code=403, detail="Invalid or revoked API key")
            
            key_doc = docs[0]
            key_data = key_doc.to_dict()
            
            # Check if the key is enabled
            if not key_data.get('enabled', False):
                raise HTTPException(status_code=403, detail="API key is disabled")
            
            # Check quota limits
            usage_count = key_data.get('usage_count', 0)
            quota_limit = key_data.get('quota_limit', 1000)
            
            if usage_count >= quota_limit:
                raise HTTPException(
                    status_code=429, 
                    detail=f"API quota exceeded. Used: {usage_count}/{quota_limit}"
                )
            
            return {
                'project': key_data.get('project', 'unknown'),
                'usage_count': usage_count,
                'quota_limit': quota_limit,
                'enabled': key_data.get('enabled', False),
                'doc_id': key_doc.id
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error validating API key: {e}")
            raise HTTPException(status_code=500, detail="Internal server error during authentication")
    
    def increment_usage(self, doc_id: str) -> None:
        """
        Increment the usage count for an API key.
        
        Args:
            doc_id: The document ID of the API key
        """
        try:
            api_key_ref = self.db.collection('api_keys').document(doc_id)
            api_key_ref.update({
                'usage_count': firestore.Increment(1)
            })
            logger.debug(f"Incremented usage count for API key doc: {doc_id}")
            
        except Exception as e:
            logger.error(f"Error incrementing usage count: {e}")
            # Don't raise exception here as it's not critical
    
    def log_usage(self, api_key_info: Dict, prompt: str, context: Dict, log_enabled: bool = False) -> None:
        """
        Log API usage to Firestore if logging is enabled.
        
        Args:
            api_key_info: Information about the API key
            prompt: The generated prompt (cleaned)
            context: The extracted context
            log_enabled: Whether to log this request
        """
        if not log_enabled:
            return
        
        try:
            from datetime import datetime
            
            log_data = {
                'project': api_key_info.get('project'),
                'prompt': prompt,
                'context': context,
                'timestamp': datetime.utcnow(),
                'usage_count_at_time': api_key_info.get('usage_count', 0)
            }
            
            # Add to logs collection
            self.db.collection('logs').add(log_data)
            logger.debug("Logged API usage to Firestore")
            
        except Exception as e:
            logger.error(f"Error logging API usage: {e}")
            # Don't raise exception here as logging is not critical
    
    def get_api_key_stats(self, api_key: str) -> Dict[str, any]:
        """
        Get statistics for an API key.
        
        Args:
            api_key: The API key to get stats for
            
        Returns:
            Dict: Statistics about the API key usage
        """
        try:
            api_keys_ref = self.db.collection('api_keys')
            query = api_keys_ref.where('api_key', '==', api_key).limit(1)
            docs = list(query.stream())
            
            if not docs:
                raise HTTPException(status_code=404, detail="API key not found")
            
            key_data = docs[0].to_dict()
            
            return {
                'project': key_data.get('project'),
                'usage_count': key_data.get('usage_count', 0),
                'quota_limit': key_data.get('quota_limit', 1000),
                'enabled': key_data.get('enabled', False),
                'remaining_quota': key_data.get('quota_limit', 1000) - key_data.get('usage_count', 0)
            }
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting API key stats: {e}")
            raise HTTPException(status_code=500, detail="Internal server error")


# Import firestore for increment operation
try:
    from google.cloud import firestore
except ImportError:
    logger.warning("google.cloud.firestore not available, usage increment will be skipped")
    firestore = None


# Global instance for reuse
_prompt_auth = None

def get_prompt_auth() -> PromptAPIAuth:
    """Get a singleton instance of the prompt API auth."""
    global _prompt_auth
    if _prompt_auth is None:
        _prompt_auth = PromptAPIAuth()
    return _prompt_auth
