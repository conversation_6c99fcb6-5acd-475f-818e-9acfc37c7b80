#!/usr/bin/env python3
"""
Test script for the prompt construction API.
"""

import asyncio
import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.pii_scrubber import get_pii_scrubber
from utils.context_parser import get_context_parser
from utils.prompt_builder import get_prompt_builder

def test_pii_scrubber():
    """Test the PII scrubber functionality."""
    print("🧪 Testing PII Scrubber...")
    
    try:
        scrubber = get_pii_scrubber()
        
        test_text = "Hey I'm <PERSON>, 28, building a wellness app for women. Email: <EMAIL>, phone: ************"
        
        print(f"Original: {test_text}")
        
        cleaned = scrubber.scrub_pii(test_text)
        print(f"Cleaned: {cleaned}")
        
        entities = scrubber.get_detected_entities(test_text)
        print(f"Detected entities: {entities}")
        
        print("✅ PII Scrubber test passed\n")
        return True
        
    except Exception as e:
        print(f"❌ PII Scrubber test failed: {e}\n")
        return False

def test_context_parser():
    """Test the context parser functionality."""
    print("🧪 Testing Context Parser...")
    
    try:
        parser = get_context_parser()
        
        test_cases = [
            "Hey I'm <PERSON> Doe, 28, building a wellness app for women.",
            "I'm a 25-year-old male developer working on a fitness app.",
            "Looking to create a productivity tool for students.",
            "35 year old woman needs help with budgeting software"
        ]
        
        for test_text in test_cases:
            context = parser.extract_all_context(test_text)
            print(f"Text: {test_text}")
            print(f"Context: {context}")
            print()
        
        print("✅ Context Parser test passed\n")
        return True
        
    except Exception as e:
        print(f"❌ Context Parser test failed: {e}\n")
        return False

def test_prompt_builder():
    """Test the prompt builder functionality."""
    print("🧪 Testing Prompt Builder...")
    
    try:
        builder = get_prompt_builder()
        
        test_cases = [
            {
                "cleaned_text": "Hey I'm [PERSON], 28, building a wellness app for women. Email: [EMAIL]",
                "context": {"age": 28, "gender": "female", "goal": "building a wellness app for women"}
            },
            {
                "cleaned_text": "I'm a 25-year-old [PERSON] developer working on a fitness app.",
                "context": {"age": 25, "gender": "male", "goal": "working on a fitness app"}
            },
            {
                "cleaned_text": "Looking to create a productivity tool for students.",
                "context": {"age": None, "gender": None, "goal": "create a productivity tool for students"}
            },
            {
                "cleaned_text": "35 year old [PERSON] needs help with budgeting software",
                "context": {"age": 35, "gender": "female", "goal": "help with budgeting software"}
            }
        ]
        
        for i, test_case in enumerate(test_cases):
            prompt = builder.build_prompt(test_case["cleaned_text"], test_case["context"])
            metadata = builder.get_prompt_metadata(test_case["context"])
            
            print(f"Test case {i+1}:")
            print(f"  Context: {test_case['context']}")
            print(f"  Prompt: {prompt}")
            print(f"  Metadata: {metadata}")
            print()
        
        print("✅ Prompt Builder test passed\n")
        return True
        
    except Exception as e:
        print(f"❌ Prompt Builder test failed: {e}\n")
        return False

def test_full_pipeline():
    """Test the complete pipeline."""
    print("🧪 Testing Full Pipeline...")
    
    try:
        scrubber = get_pii_scrubber()
        parser = get_context_parser()
        builder = get_prompt_builder()
        
        test_input = "Hey I'm Jane Doe, 28, building a wellness app for women. Email: <EMAIL>"
        
        print(f"Input: {test_input}")
        
        # Step 1: Extract context
        context = parser.extract_all_context(test_input)
        print(f"Extracted context: {context}")
        
        # Step 2: Clean PII
        cleaned = scrubber.scrub_pii(test_input)
        print(f"Cleaned text: {cleaned}")
        
        # Step 3: Build prompt
        prompt = builder.build_prompt(cleaned, context)
        print(f"Final prompt: {prompt}")
        
        # Validate
        is_valid = builder.validate_prompt(prompt)
        print(f"Prompt valid: {is_valid}")
        
        print("✅ Full Pipeline test passed\n")
        return True
        
    except Exception as e:
        print(f"❌ Full Pipeline test failed: {e}\n")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting Prompt API Tests...\n")
    
    tests = [
        test_context_parser,  # Test this first as it doesn't require Presidio
        test_prompt_builder,  # Test this second
        test_pii_scrubber,    # Test Presidio PII scrubber
        test_full_pipeline,   # Test the complete pipeline
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
