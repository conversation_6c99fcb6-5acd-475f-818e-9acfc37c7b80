aiohttp==3.11.11
annotated-types==0.7.0
anyio==4.9.0
beautifulsoup4==4.12.3
CacheControl==0.14.2
cachetools==5.5.2
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
cryptography==44.0.2
dnspython==2.6.1  # Required by email-validator
email-validator==2.1.1
fastapi==0.115.12
firebase-admin==6.7.0
google-api-core==2.24.2
google-api-python-client==2.166.0
google-auth==2.38.0
google-auth-httplib2==0.2.0
google-cloud-core==2.4.3
google-cloud-firestore==2.20.1
google-cloud-storage==3.1.0
google-crc32c==1.7.1
google-resumable-media==2.7.2
googleapis-common-protos==1.69.2
grpcio==1.71.0
grpcio-status==1.71.0
gunicorn==23.0.0
h11==0.14.0
httplib2==0.22.0
httptools==0.6.4
httpx==0.27.0
idna==3.10
msgpack==1.1.0
numpy==1.26.4
openai==1.58.1
packaging==24.2
proto-plus==1.26.1
protobuf==5.29.4
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycparser==2.22
pydantic==2.11.3
pydantic_core==2.33.1
PyJWT==2.10.1
pyparsing==3.2.3
python-dotenv==1.1.0
python-multipart==0.0.9  # For form data handling
python-slugify==8.0.4
PyYAML==6.0.2
resend
requests==2.32.3
rsa==4.9
sniffio==1.3.1
starlette==0.46.1
stripe==12.0.0
text-unidecode==1.3
typing-extensions==4.13.2
typing-inspect==0.9.0  # Renamed from typing-inspection
uritemplate==4.1.1
urllib3==2.4.0
uvicorn==0.34.0
uvloop==0.21.0
watchfiles==1.0.5
websockets==15.0.1
presidio-analyzer==2.2.355
presidio-anonymizer==2.2.355
spacy==3.7.6
https://github.com/explosion/spacy-models/releases/download/en_core_web_sm-3.7.1/en_core_web_sm-3.7.1-py3-none-any.whl
