"""
Pydantic models for the prompt construction API.
"""

from pydantic import BaseModel, Field, validator
from typing import Optional, Dict, Union, List
from datetime import datetime

class PromptRequest(BaseModel):
    """Request model for the build-prompt endpoint."""
    
    user_input: str = Field(
        ..., 
        description="Raw user input text to process",
        min_length=1,
        max_length=5000,
        example="Hey I'm <PERSON>, 28, building a wellness app for women. Email: <EMAIL>"
    )
    
    log: bool = Field(
        default=False,
        description="Whether to log this request to Firestore"
    )
    
    @validator('user_input')
    def validate_user_input(cls, v):
        if not v or not v.strip():
            raise ValueError('user_input cannot be empty')
        return v.strip()

class ContextInfo(BaseModel):
    """Model for extracted context information."""
    
    age: Optional[int] = Field(
        None,
        description="Extracted age from user input",
        ge=13,
        le=100,
        example=28
    )
    
    gender: Optional[str] = Field(
        None,
        description="Extracted gender from user input",
        regex="^(male|female)$",
        example="female"
    )
    
    goal: Optional[str] = Field(
        None,
        description="Extracted goal or intent from user input",
        max_length=500,
        example="build a wellness app for women"
    )

class PromptResponse(BaseModel):
    """Response model for the build-prompt endpoint."""
    
    prompt: str = Field(
        ...,
        description="Generated prompt with PII removed",
        example="Suggest tools for a 28-year-old female building a wellness app for women."
    )
    
    context: ContextInfo = Field(
        ...,
        description="Extracted context information"
    )

class PromptError(BaseModel):
    """Error response model."""
    
    error: str = Field(
        ...,
        description="Error message"
    )
    
    detail: Optional[str] = Field(
        None,
        description="Additional error details"
    )

class APIKeyStats(BaseModel):
    """Model for API key statistics."""
    
    project: str = Field(
        ...,
        description="Project name associated with the API key"
    )
    
    usage_count: int = Field(
        ...,
        description="Current usage count",
        ge=0
    )
    
    quota_limit: int = Field(
        ...,
        description="Maximum allowed usage",
        ge=1
    )
    
    remaining_quota: int = Field(
        ...,
        description="Remaining quota",
        ge=0
    )
    
    enabled: bool = Field(
        ...,
        description="Whether the API key is enabled"
    )

class DetectedEntity(BaseModel):
    """Model for detected PII entities."""
    
    entity_type: str = Field(
        ...,
        description="Type of detected entity (e.g., PERSON, EMAIL_ADDRESS)"
    )
    
    start: int = Field(
        ...,
        description="Start position in text",
        ge=0
    )
    
    end: int = Field(
        ...,
        description="End position in text",
        ge=0
    )
    
    score: float = Field(
        ...,
        description="Confidence score",
        ge=0.0,
        le=1.0
    )
    
    text: str = Field(
        ...,
        description="The detected entity text"
    )

class PIIAnalysisResponse(BaseModel):
    """Response model for PII analysis endpoint."""
    
    original_text: str = Field(
        ...,
        description="Original input text"
    )
    
    cleaned_text: str = Field(
        ...,
        description="Text with PII removed"
    )
    
    detected_entities: List[DetectedEntity] = Field(
        ...,
        description="List of detected PII entities"
    )
    
    context: ContextInfo = Field(
        ...,
        description="Extracted context information"
    )

class HealthCheckResponse(BaseModel):
    """Response model for health check endpoint."""
    
    status: str = Field(
        default="healthy",
        description="Service status"
    )
    
    timestamp: datetime = Field(
        default_factory=datetime.utcnow,
        description="Current timestamp"
    )
    
    version: str = Field(
        default="1.0.0",
        description="API version"
    )
    
    services: Dict[str, str] = Field(
        default_factory=dict,
        description="Status of dependent services"
    )
