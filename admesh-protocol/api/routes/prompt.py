"""
FastAPI routes for the prompt construction API with Presidio PII removal.
"""

import logging
from fastapi import APIRouter, HTTPException, <PERSON><PERSON>, Depends
from fastapi.responses import JSONResponse
from typing import Optional

# Import our utilities
from utils.pii_scrubber import get_pii_scrubber
from utils.context_parser import get_context_parser
from utils.prompt_builder import get_prompt_builder
from firebase.prompt_auth import get_prompt_auth

# Import models
from api.models.prompt import (
    PromptRequest, PromptResponse, PromptError, APIKeyStats,
    PIIAnalysisResponse, HealthCheckResponse, ContextInfo
)

logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

# Dependency to validate API key
async def validate_api_key(x_admesh_key: Optional[str] = Header(None)):
    """Dependency to validate the x-admesh-key header."""
    if not x_admesh_key:
        raise HTTPException(
            status_code=401,
            detail="Missing x-admesh-key header"
        )
    
    auth = get_prompt_auth()
    return auth.validate_api_key(x_admesh_key)

@router.post("/build-prompt", response_model=PromptResponse)
async def build_prompt(
    request: PromptRequest,
    api_key_info: dict = Depends(validate_api_key)
):
    """
    Build a structured prompt from user input with PII removal.
    
    This endpoint:
    1. Removes PII using Presidio
    2. Extracts context (age, gender, goal)
    3. Reconstructs a natural prompt
    4. Validates API key and tracks usage
    5. Optionally logs the request
    """
    try:
        logger.info(f"Processing prompt request for project: {api_key_info.get('project')}")
        
        # Initialize utilities
        pii_scrubber = get_pii_scrubber()
        context_parser = get_context_parser()
        prompt_builder = get_prompt_builder()
        auth = get_prompt_auth()
        
        # Step 1: Extract context before PII removal (to preserve age, gender, goals)
        context = context_parser.extract_all_context(request.user_input)
        logger.debug(f"Extracted context: {context}")
        
        # Step 2: Remove PII while preserving age-related information
        cleaned_text = pii_scrubber.scrub_pii(
            request.user_input,
            preserve_entities=[]  # We extract context first, so we can remove all PII
        )
        logger.debug(f"Cleaned text: {cleaned_text}")
        
        # Step 3: Build the structured prompt
        prompt = prompt_builder.build_prompt(cleaned_text, context)
        logger.debug(f"Generated prompt: {prompt}")
        
        # Step 4: Validate the prompt
        if not prompt_builder.validate_prompt(prompt):
            logger.warning("Generated prompt failed validation, using fallback")
            prompt = "Suggest relevant tools and resources."
        
        # Step 5: Increment usage count
        auth.increment_usage(api_key_info['doc_id'])
        
        # Step 6: Log usage if requested
        if request.log:
            auth.log_usage(api_key_info, prompt, context, log_enabled=True)
        
        # Step 7: Prepare response
        response = PromptResponse(
            prompt=prompt,
            context=ContextInfo(**context)
        )
        
        logger.info(f"Successfully processed prompt request for project: {api_key_info.get('project')}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing prompt request: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error while processing prompt"
        )

@router.post("/analyze-pii", response_model=PIIAnalysisResponse)
async def analyze_pii(
    request: PromptRequest,
    api_key_info: dict = Depends(validate_api_key)
):
    """
    Analyze text for PII entities without building a prompt.
    
    This endpoint is useful for debugging and understanding
    what PII entities are detected in the input text.
    """
    try:
        logger.info(f"Processing PII analysis for project: {api_key_info.get('project')}")
        
        # Initialize utilities
        pii_scrubber = get_pii_scrubber()
        context_parser = get_context_parser()
        auth = get_prompt_auth()
        
        # Extract context
        context = context_parser.extract_all_context(request.user_input)
        
        # Get detected entities
        detected_entities = pii_scrubber.get_detected_entities(request.user_input)
        
        # Clean the text
        cleaned_text = pii_scrubber.scrub_pii(request.user_input)
        
        # Increment usage count
        auth.increment_usage(api_key_info['doc_id'])
        
        # Prepare response
        response = PIIAnalysisResponse(
            original_text=request.user_input,
            cleaned_text=cleaned_text,
            detected_entities=detected_entities,
            context=ContextInfo(**context)
        )
        
        logger.info(f"Successfully analyzed PII for project: {api_key_info.get('project')}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing PII: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error while analyzing PII"
        )

@router.get("/api-key-stats", response_model=APIKeyStats)
async def get_api_key_stats(
    api_key_info: dict = Depends(validate_api_key)
):
    """
    Get statistics for the current API key.
    """
    try:
        # The api_key_info already contains the stats we need
        stats = APIKeyStats(
            project=api_key_info.get('project', 'unknown'),
            usage_count=api_key_info.get('usage_count', 0),
            quota_limit=api_key_info.get('quota_limit', 1000),
            remaining_quota=api_key_info.get('quota_limit', 1000) - api_key_info.get('usage_count', 0),
            enabled=api_key_info.get('enabled', False)
        )
        
        return stats
        
    except Exception as e:
        logger.error(f"Error getting API key stats: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error while getting stats"
        )

@router.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """
    Health check endpoint to verify service status.
    """
    try:
        # Test basic functionality
        services = {}
        
        # Test PII scrubber
        try:
            pii_scrubber = get_pii_scrubber()
            test_result = pii_scrubber.scrub_pii("Test text")
            services["pii_scrubber"] = "healthy"
        except Exception as e:
            services["pii_scrubber"] = f"error: {str(e)}"
        
        # Test context parser
        try:
            context_parser = get_context_parser()
            test_context = context_parser.extract_all_context("I'm 25 years old")
            services["context_parser"] = "healthy"
        except Exception as e:
            services["context_parser"] = f"error: {str(e)}"
        
        # Test prompt builder
        try:
            prompt_builder = get_prompt_builder()
            test_prompt = prompt_builder.build_prompt("test", {"age": 25, "gender": None, "goal": None})
            services["prompt_builder"] = "healthy"
        except Exception as e:
            services["prompt_builder"] = f"error: {str(e)}"
        
        # Test Firebase connection
        try:
            auth = get_prompt_auth()
            services["firebase"] = "healthy"
        except Exception as e:
            services["firebase"] = f"error: {str(e)}"
        
        # Determine overall status
        status = "healthy" if all(s == "healthy" for s in services.values()) else "degraded"
        
        return HealthCheckResponse(
            status=status,
            services=services
        )
        
    except Exception as e:
        logger.error(f"Error in health check: {e}")
        return HealthCheckResponse(
            status="unhealthy",
            services={"error": str(e)}
        )
