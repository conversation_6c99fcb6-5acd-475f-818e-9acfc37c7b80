"""
PII Scrubber using Microsoft Presidio for removing sensitive information
while preserving context like age, gender, and user intent.
"""

import logging
from typing import Dict, List, Optional
from presidio_analyzer import AnalyzerEngine
from presidio_anonymizer import AnonymizerEngine
from presidio_anonymizer.entities import RecognizerResult, OperatorConfig

logger = logging.getLogger(__name__)

class PIIScrubber:
    """
    A class to handle PII removal using Microsoft Presidio while preserving
    important context information like age, gender, and user goals.
    """
    
    def __init__(self):
        """Initialize the PII scrubber with Presidio engines."""
        try:
            self.analyzer = AnalyzerEngine()
            self.anonymizer = AnonymizerEngine()
            logger.info("✅ PII Scrubber initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize PII Scrubber: {e}")
            raise
    
    def scrub_pii(self, text: str, preserve_entities: Optional[List[str]] = None) -> str:
        """
        Remove PII from text while preserving specified entities.
        
        Args:
            text: The input text to scrub
            preserve_entities: List of entity types to preserve (e.g., ['AGE'])
            
        Returns:
            str: The scrubbed text with PII removed
        """
        if not text or not text.strip():
            return text
            
        try:
            # Default entities to remove (excluding age which we want to preserve)
            entities_to_remove = [
                "PERSON", "EMAIL_ADDRESS", "PHONE_NUMBER", "CREDIT_CARD",
                "IBAN_CODE", "IP_ADDRESS", "LOCATION", "URL", "US_SSN",
                "US_DRIVER_LICENSE", "US_PASSPORT", "DATE_TIME"
            ]
            
            # Remove entities that should be preserved
            if preserve_entities:
                entities_to_remove = [
                    entity for entity in entities_to_remove 
                    if entity not in preserve_entities
                ]
            
            # Analyze the text for PII
            analyzer_results = self.analyzer.analyze(
                text=text,
                entities=entities_to_remove,
                language='en'
            )
            
            # Configure anonymization operators
            operators = {
                "PERSON": OperatorConfig("replace", {"new_value": "[PERSON]"}),
                "EMAIL_ADDRESS": OperatorConfig("replace", {"new_value": "[EMAIL]"}),
                "PHONE_NUMBER": OperatorConfig("replace", {"new_value": "[PHONE]"}),
                "CREDIT_CARD": OperatorConfig("replace", {"new_value": "[CREDIT_CARD]"}),
                "IBAN_CODE": OperatorConfig("replace", {"new_value": "[IBAN]"}),
                "IP_ADDRESS": OperatorConfig("replace", {"new_value": "[IP_ADDRESS]"}),
                "LOCATION": OperatorConfig("replace", {"new_value": "[LOCATION]"}),
                "URL": OperatorConfig("replace", {"new_value": "[URL]"}),
                "US_SSN": OperatorConfig("replace", {"new_value": "[SSN]"}),
                "US_DRIVER_LICENSE": OperatorConfig("replace", {"new_value": "[DRIVER_LICENSE]"}),
                "US_PASSPORT": OperatorConfig("replace", {"new_value": "[PASSPORT]"}),
                "DATE_TIME": OperatorConfig("replace", {"new_value": "[DATE]"}),
            }
            
            # Anonymize the text
            anonymized_result = self.anonymizer.anonymize(
                text=text,
                analyzer_results=analyzer_results,
                operators=operators
            )
            
            return anonymized_result.text
            
        except Exception as e:
            logger.error(f"Error scrubbing PII from text: {e}")
            # Return original text if scrubbing fails
            return text
    
    def get_detected_entities(self, text: str) -> List[Dict]:
        """
        Get a list of detected PII entities in the text.
        
        Args:
            text: The input text to analyze
            
        Returns:
            List[Dict]: List of detected entities with their types and positions
        """
        if not text or not text.strip():
            return []
            
        try:
            analyzer_results = self.analyzer.analyze(text=text, language='en')
            
            entities = []
            for result in analyzer_results:
                entities.append({
                    "entity_type": result.entity_type,
                    "start": result.start,
                    "end": result.end,
                    "score": result.score,
                    "text": text[result.start:result.end]
                })
            
            return entities
            
        except Exception as e:
            logger.error(f"Error detecting entities in text: {e}")
            return []
    
    def scrub_with_custom_replacements(self, text: str, custom_operators: Dict[str, OperatorConfig]) -> str:
        """
        Scrub PII with custom replacement operators.
        
        Args:
            text: The input text to scrub
            custom_operators: Dictionary of entity types to custom operators
            
        Returns:
            str: The scrubbed text
        """
        if not text or not text.strip():
            return text
            
        try:
            # Analyze the text for all PII types
            analyzer_results = self.analyzer.analyze(text=text, language='en')
            
            # Use custom operators if provided, otherwise use defaults
            operators = custom_operators if custom_operators else {
                "DEFAULT": OperatorConfig("replace", {"new_value": "[REDACTED]"})
            }
            
            # Anonymize the text
            anonymized_result = self.anonymizer.anonymize(
                text=text,
                analyzer_results=analyzer_results,
                operators=operators
            )
            
            return anonymized_result.text
            
        except Exception as e:
            logger.error(f"Error scrubbing PII with custom operators: {e}")
            return text


# Global instance for reuse
_pii_scrubber = None

def get_pii_scrubber() -> PIIScrubber:
    """Get a singleton instance of the PII scrubber."""
    global _pii_scrubber
    if _pii_scrubber is None:
        _pii_scrubber = PIIScrubber()
    return _pii_scrubber
