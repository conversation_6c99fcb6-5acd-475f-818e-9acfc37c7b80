"""
Context Parser for extracting age, gender, and user goals from text
using regex patterns and NLP techniques.
"""

import re
import logging
from typing import Dict, Optional, List, Union

logger = logging.getLogger(__name__)

class ContextParser:
    """
    A class to extract contextual information like age, gender, and goals
    from user input text.
    """
    
    def __init__(self):
        """Initialize the context parser with regex patterns."""
        # Age patterns
        self.age_patterns = [
            r'\b(\d{1,2})\s*(?:years?\s*old|yr\s*old|y\.?o\.?)\b',
            r'\b(?:age|aged?)\s*(\d{1,2})\b',
            r'\b(\d{1,2})\s*(?:year|yr)\s*old\b',
            r'\bI\'?m\s*(\d{1,2})\b',
            r'\b(\d{1,2})\s*(?:years?\s*of\s*age)\b',
            r'\b(\d{1,2})-year-old\b',
            r'\b(\d{1,2})\s*year\s*old\b'
        ]
        
        # Gender patterns
        self.gender_patterns = {
            'male': [
                r'\b(?:I\'?m\s*a?\s*)?(?:man|male|guy|boy|gentleman|dude|mr\.?|mister)\b',
                r'\b(?:for\s*)?(?:men|males|guys|boys)\b'
            ],
            'female': [
                r'\b(?:I\'?m\s*a?\s*)?(?:woman|female|girl|lady|gal|ms\.?|miss|mrs\.?)\b',
                r'\b(?:for\s*)?(?:women|females|girls|ladies)\b'
            ]
        }
        
        # Goal/intent patterns
        self.goal_patterns = [
            r'\b(?:building|creating|developing|making|designing)\s+(?:a|an)?\s*([^.!?]+?)(?:\.|$)',
            r'\b(?:want|need|looking)\s+to\s+(?:build|create|develop|make|design)\s+(?:a|an)?\s*([^.!?]+?)(?:\.|$)',
            r'\b(?:working\s+on|building)\s+(?:a|an)?\s*([^.!?]+?)(?:\.|$)',
            r'\b(?:for|to\s+help\s+with)\s+([^.!?]+?)(?:\.|$)',
            r'\b(?:app|application|website|platform|tool|service|system)\s+(?:for|to\s+help\s+with)?\s*([^.!?]+?)(?:\.|$)'
        ]
        
        logger.info("✅ Context Parser initialized successfully")
    
    def extract_age(self, text: str) -> Optional[int]:
        """
        Extract age from text using regex patterns.
        
        Args:
            text: The input text to analyze
            
        Returns:
            Optional[int]: The extracted age or None if not found
        """
        if not text:
            return None
            
        text_lower = text.lower()
        
        for pattern in self.age_patterns:
            matches = re.finditer(pattern, text_lower, re.IGNORECASE)
            for match in matches:
                try:
                    age = int(match.group(1))
                    # Validate reasonable age range
                    if 13 <= age <= 100:
                        return age
                except (ValueError, IndexError):
                    continue
        
        return None
    
    def extract_gender(self, text: str) -> Optional[str]:
        """
        Extract gender from text using regex patterns.
        
        Args:
            text: The input text to analyze
            
        Returns:
            Optional[str]: The extracted gender ('male' or 'female') or None if not found
        """
        if not text:
            return None
            
        text_lower = text.lower()
        
        # Check for male indicators
        for pattern in self.gender_patterns['male']:
            if re.search(pattern, text_lower, re.IGNORECASE):
                return 'male'
        
        # Check for female indicators
        for pattern in self.gender_patterns['female']:
            if re.search(pattern, text_lower, re.IGNORECASE):
                return 'female'
        
        return None
    
    def extract_goal(self, text: str) -> Optional[str]:
        """
        Extract user goal/intent from text using regex patterns.
        
        Args:
            text: The input text to analyze
            
        Returns:
            Optional[str]: The extracted goal or None if not found
        """
        if not text:
            return None
            
        text_lower = text.lower()
        
        for pattern in self.goal_patterns:
            matches = re.finditer(pattern, text_lower, re.IGNORECASE)
            for match in matches:
                try:
                    goal = match.group(1).strip()
                    # Clean up the goal text
                    goal = re.sub(r'\s+', ' ', goal)  # Normalize whitespace
                    goal = goal.strip(',.')  # Remove trailing punctuation
                    
                    # Filter out very short or generic goals
                    if len(goal) > 3 and not re.match(r'^\w{1,3}$', goal):
                        return goal
                except (IndexError, AttributeError):
                    continue
        
        return None
    
    def extract_all_context(self, text: str) -> Dict[str, Union[int, str, None]]:
        """
        Extract all contextual information from text.
        
        Args:
            text: The input text to analyze
            
        Returns:
            Dict: Dictionary containing age, gender, and goal
        """
        if not text:
            return {"age": None, "gender": None, "goal": None}
        
        try:
            context = {
                "age": self.extract_age(text),
                "gender": self.extract_gender(text),
                "goal": self.extract_goal(text)
            }
            
            logger.debug(f"Extracted context: {context}")
            return context
            
        except Exception as e:
            logger.error(f"Error extracting context from text: {e}")
            return {"age": None, "gender": None, "goal": None}
    
    def validate_context(self, context: Dict) -> Dict[str, bool]:
        """
        Validate extracted context information.
        
        Args:
            context: Dictionary containing extracted context
            
        Returns:
            Dict: Dictionary with validation results for each field
        """
        validation = {
            "age_valid": False,
            "gender_valid": False,
            "goal_valid": False
        }
        
        # Validate age
        if context.get("age") is not None:
            age = context["age"]
            validation["age_valid"] = isinstance(age, int) and 13 <= age <= 100
        
        # Validate gender
        if context.get("gender") is not None:
            gender = context["gender"]
            validation["gender_valid"] = gender in ["male", "female"]
        
        # Validate goal
        if context.get("goal") is not None:
            goal = context["goal"]
            validation["goal_valid"] = isinstance(goal, str) and len(goal.strip()) > 3
        
        return validation


# Global instance for reuse
_context_parser = None

def get_context_parser() -> ContextParser:
    """Get a singleton instance of the context parser."""
    global _context_parser
    if _context_parser is None:
        _context_parser = ContextParser()
    return _context_parser
