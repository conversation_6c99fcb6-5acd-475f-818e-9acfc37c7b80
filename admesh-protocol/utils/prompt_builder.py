"""
Prompt Builder for reconstructing natural language prompts from cleaned data
and extracted context information.
"""

import logging
from typing import Dict, Optional, Union

logger = logging.getLogger(__name__)

class PromptBuilder:
    """
    A class to build natural language prompts from cleaned text and context.
    """
    
    def __init__(self):
        """Initialize the prompt builder."""
        self.default_templates = {
            "full_context": "Suggest tools for a {age}-year-old {gender} {goal}.",
            "age_gender": "Suggest tools for a {age}-year-old {gender}.",
            "age_goal": "Suggest tools for a {age}-year-old {goal}.",
            "gender_goal": "Suggest tools for a {gender} {goal}.",
            "age_only": "Suggest tools for a {age}-year-old.",
            "gender_only": "Suggest tools for a {gender}.",
            "goal_only": "Suggest tools for {goal}.",
            "fallback": "Suggest relevant tools and resources."
        }
        
        logger.info("✅ Prompt Builder initialized successfully")
    
    def build_prompt(self, cleaned_text: str, context: Dict[str, Union[int, str, None]]) -> str:
        """
        Build a natural language prompt from cleaned text and context.
        
        Args:
            cleaned_text: The PII-scrubbed text
            context: Dictionary containing age, gender, and goal
            
        Returns:
            str: The constructed prompt
        """
        try:
            age = context.get("age")
            gender = context.get("gender")
            goal = context.get("goal")
            
            # Determine which template to use based on available context
            if age and gender and goal:
                template = self.default_templates["full_context"]
                prompt = template.format(age=age, gender=gender, goal=goal)
            elif age and gender:
                template = self.default_templates["age_gender"]
                prompt = template.format(age=age, gender=gender)
            elif age and goal:
                template = self.default_templates["age_goal"]
                prompt = template.format(age=age, goal=goal)
            elif gender and goal:
                template = self.default_templates["gender_goal"]
                prompt = template.format(gender=gender, goal=goal)
            elif age:
                template = self.default_templates["age_only"]
                prompt = template.format(age=age)
            elif gender:
                template = self.default_templates["gender_only"]
                prompt = template.format(gender=gender)
            elif goal:
                template = self.default_templates["goal_only"]
                prompt = template.format(goal=goal)
            else:
                # Fallback: try to use cleaned text if it has meaningful content
                if cleaned_text and len(cleaned_text.strip()) > 10:
                    # Remove common PII placeholders and clean up
                    cleaned = self._clean_placeholder_text(cleaned_text)
                    if cleaned and len(cleaned.strip()) > 10:
                        prompt = f"Suggest tools for: {cleaned.strip()}"
                    else:
                        prompt = self.default_templates["fallback"]
                else:
                    prompt = self.default_templates["fallback"]
            
            return prompt
            
        except Exception as e:
            logger.error(f"Error building prompt: {e}")
            return self.default_templates["fallback"]
    
    def _clean_placeholder_text(self, text: str) -> str:
        """
        Clean PII placeholders from text to make it more readable.
        
        Args:
            text: Text containing PII placeholders
            
        Returns:
            str: Cleaned text
        """
        if not text:
            return ""
        
        # Remove common PII placeholders
        placeholders = [
            r'\[PERSON\]', r'\[EMAIL\]', r'\[PHONE\]', r'\[CREDIT_CARD\]',
            r'\[IBAN\]', r'\[IP_ADDRESS\]', r'\[LOCATION\]', r'\[URL\]',
            r'\[SSN\]', r'\[DRIVER_LICENSE\]', r'\[PASSPORT\]', r'\[DATE\]'
        ]
        
        import re
        cleaned = text
        for placeholder in placeholders:
            cleaned = re.sub(placeholder, '', cleaned, flags=re.IGNORECASE)
        
        # Clean up extra whitespace and punctuation
        cleaned = re.sub(r'\s+', ' ', cleaned)  # Normalize whitespace
        cleaned = re.sub(r'[,\s]+$', '', cleaned)  # Remove trailing commas/spaces
        cleaned = re.sub(r'^[,\s]+', '', cleaned)  # Remove leading commas/spaces
        
        return cleaned.strip()
    
    def build_custom_prompt(self, template: str, context: Dict[str, Union[int, str, None]]) -> str:
        """
        Build a prompt using a custom template.
        
        Args:
            template: Custom template string with placeholders
            context: Dictionary containing context variables
            
        Returns:
            str: The constructed prompt
        """
        try:
            # Filter out None values from context
            filtered_context = {k: v for k, v in context.items() if v is not None}
            
            # Try to format the template with available context
            prompt = template.format(**filtered_context)
            return prompt
            
        except KeyError as e:
            logger.warning(f"Missing context variable in template: {e}")
            return self.default_templates["fallback"]
        except Exception as e:
            logger.error(f"Error building custom prompt: {e}")
            return self.default_templates["fallback"]
    
    def validate_prompt(self, prompt: str) -> bool:
        """
        Validate that a prompt is reasonable and not empty.
        
        Args:
            prompt: The prompt to validate
            
        Returns:
            bool: True if prompt is valid, False otherwise
        """
        if not prompt or not prompt.strip():
            return False
        
        # Check minimum length
        if len(prompt.strip()) < 5:
            return False
        
        # Check that it's not just placeholders
        import re
        placeholder_pattern = r'\[[\w_]+\]'
        cleaned = re.sub(placeholder_pattern, '', prompt)
        if len(cleaned.strip()) < 5:
            return False
        
        return True
    
    def get_prompt_metadata(self, context: Dict[str, Union[int, str, None]]) -> Dict[str, Union[str, bool]]:
        """
        Get metadata about the prompt construction process.
        
        Args:
            context: Dictionary containing context information
            
        Returns:
            Dict: Metadata about the prompt
        """
        age = context.get("age")
        gender = context.get("gender")
        goal = context.get("goal")
        
        metadata = {
            "has_age": age is not None,
            "has_gender": gender is not None,
            "has_goal": goal is not None,
            "context_completeness": "full" if all([age, gender, goal]) else "partial",
            "template_used": "unknown"
        }
        
        # Determine which template would be used
        if age and gender and goal:
            metadata["template_used"] = "full_context"
        elif age and gender:
            metadata["template_used"] = "age_gender"
        elif age and goal:
            metadata["template_used"] = "age_goal"
        elif gender and goal:
            metadata["template_used"] = "gender_goal"
        elif age:
            metadata["template_used"] = "age_only"
        elif gender:
            metadata["template_used"] = "gender_only"
        elif goal:
            metadata["template_used"] = "goal_only"
        else:
            metadata["template_used"] = "fallback"
        
        return metadata


# Global instance for reuse
_prompt_builder = None

def get_prompt_builder() -> PromptBuilder:
    """Get a singleton instance of the prompt builder."""
    global _prompt_builder
    if _prompt_builder is None:
        _prompt_builder = PromptBuilder()
    return _prompt_builder
